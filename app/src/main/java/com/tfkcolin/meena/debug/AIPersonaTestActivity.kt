package com.tfkcolin.meena.debug

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.lifecycleScope
import com.tfkcolin.meena.mock.utils.MockTestingUtils
import com.tfkcolin.meena.mock.utils.AIPersonaInfo
import com.tfkcolin.meena.mock.utils.AIPersonaSetupStatus
import com.tfkcolin.meena.ui.theme.MeenaTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Debug activity for testing AI persona setup and functionality.
 * This activity is only available in debug builds for testing purposes.
 */
@AndroidEntryPoint
class AIPersonaTestActivity : ComponentActivity() {

    @Inject
    lateinit var mockTestingUtils: MockTestingUtils

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            MeenaTheme {
                AIPersonaTestScreen(mockTestingUtils)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AIPersonaTestScreen(mockTestingUtils: MockTestingUtils) {
    var setupStatus by remember { mutableStateOf<AIPersonaSetupStatus?>(null) }
    var aiPersonas by remember { mutableStateOf<List<AIPersonaInfo>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }

    LaunchedEffect(Unit) {
        loadData(mockTestingUtils) { status, personas ->
            setupStatus = status
            aiPersonas = personas
            isLoading = false
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("AI Persona Test") }
            )
        }
    ) { paddingValues ->
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    SetupStatusCard(setupStatus)
                }
                
                item {
                    Text(
                        text = "AI Personas (${aiPersonas.size})",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                items(aiPersonas) { persona ->
                    AIPersonaCard(persona)
                }
                
                item {
                    Button(
                        onClick = {
                            // Print debug info to console
                            mockTestingUtils.printAIPersonasDebugInfo()
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("Print Debug Info to Console")
                    }
                }
                
                item {
                    Button(
                        onClick = {
                            // Reset mock data
                            isLoading = true
                            kotlinx.coroutines.GlobalScope.launch {
                                mockTestingUtils.resetMockDataWithAIPersonas()
                                loadData(mockTestingUtils) { status, personas ->
                                    setupStatus = status
                                    aiPersonas = personas
                                    isLoading = false
                                }
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("Reset Mock Data")
                    }
                }
            }
        }
    }
}

@Composable
fun SetupStatusCard(status: AIPersonaSetupStatus?) {
    if (status == null) return
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (status.isSetupComplete) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Setup Status",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = if (status.isSetupComplete) "✅ Complete" else "❌ Incomplete",
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text("Current User: ${if (status.hasCurrentUser) "✅" else "❌"}")
            Text("Total Users: ${status.totalUsers}")
            Text("AI Users: ${status.aiUsersCount}/${status.expectedAIPersonas}")
            Text("AI Contacts: ${status.aiContactsCount}")
            Text("AI Chats: ${status.aiChatsCount}")
        }
    }
}

@Composable
fun AIPersonaCard(persona: AIPersonaInfo) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = persona.name,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Text(
                text = "@${persona.userHandle}",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = persona.description,
                style = MaterialTheme.typography.bodyMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Status: ${if (persona.isOnline) "🟢 Online" else "🔴 Offline"}",
                    style = MaterialTheme.typography.bodySmall
                )
                
                Text(
                    text = "ID: ${persona.userId}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.outline
                )
            }
        }
    }
}

private suspend fun loadData(
    mockTestingUtils: MockTestingUtils,
    onResult: (AIPersonaSetupStatus, List<AIPersonaInfo>) -> Unit
) {
    val status = mockTestingUtils.verifyAIPersonasSetup()
    val personas = mockTestingUtils.getAllAIPersonasInfo()
    onResult(status, personas)
}
