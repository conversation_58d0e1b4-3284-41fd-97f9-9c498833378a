package com.tfkcolin.meena.mock.utils

import com.tfkcolin.meena.mock.ai.AIPersonaFactory
import com.tfkcolin.meena.mock.data.MockDataGenerator
import com.tfkcolin.meena.mock.storage.MockDataStorage
import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.data.models.User
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for testing mock backend functionality with AI personas.
 */
@Singleton
class MockTestingUtils @Inject constructor(
    private val mockDataStorage: MockDataStorage
) {
    
    /**
     * Get all AI persona contacts for the current user.
     */
    fun getAIPersonaContacts(): List<Contact> {
        val currentUser = mockDataStorage.getCurrentUser() ?: return emptyList()
        val userContacts = mockDataStorage.getContacts(currentUser.userId)
        
        return userContacts.filter { contact ->
            MockDataGenerator.isAIPersonaUser(contact.contactId)
        }
    }
    
    /**
     * Get all AI persona chats for the current user.
     */
    fun getAIPersonaChats(): List<Chat> {
        val currentUser = mockDataStorage.getCurrentUser() ?: return emptyList()
        val allChats = mockDataStorage.getChats().values
        
        return allChats.filter { chat ->
            chat.getParticipantIdsList().contains(currentUser.userId) &&
            chat.getParticipantIdsList().any { participantId ->
                MockDataGenerator.isAIPersonaUser(participantId)
            }
        }
    }
    
    /**
     * Get AI persona information by contact ID.
     */
    fun getAIPersonaInfo(contactId: String): AIPersonaInfo? {
        if (!MockDataGenerator.isAIPersonaUser(contactId)) return null
        
        val personaId = MockDataGenerator.getAIPersonaIdFromUserId(contactId) ?: return null
        val persona = AIPersonaFactory.getPersonaById(personaId) ?: return null
        val user = mockDataStorage.getUser(contactId) ?: return null
        
        return AIPersonaInfo(
            personaId = personaId,
            userId = contactId,
            name = persona.name,
            description = persona.description,
            userHandle = user.userHandle,
            displayName = user.displayName ?: persona.name,
            isOnline = true // AI personas are always online
        )
    }
    
    /**
     * Get all available AI personas information.
     */
    fun getAllAIPersonasInfo(): List<AIPersonaInfo> {
        return AIPersonaFactory.getAllPersonas().mapNotNull { persona ->
            val userId = "ai_${persona.id}"
            val user = mockDataStorage.getUser(userId)
            if (user != null) {
                AIPersonaInfo(
                    personaId = persona.id,
                    userId = userId,
                    name = persona.name,
                    description = persona.description,
                    userHandle = user.userHandle,
                    displayName = user.displayName ?: persona.name,
                    isOnline = true
                )
            } else null
        }
    }
    
    /**
     * Check if mock data contains AI personas.
     */
    fun verifyAIPersonasSetup(): AIPersonaSetupStatus {
        val currentUser = mockDataStorage.getCurrentUser()
        val allUsers = mockDataStorage.getUsers()
        val aiUsers = allUsers.values.filter { MockDataGenerator.isAIPersonaUser(it.userId) }
        val aiContacts = if (currentUser != null) getAIPersonaContacts() else emptyList()
        val aiChats = if (currentUser != null) getAIPersonaChats() else emptyList()
        
        return AIPersonaSetupStatus(
            hasCurrentUser = currentUser != null,
            totalUsers = allUsers.size,
            aiUsersCount = aiUsers.size,
            aiContactsCount = aiContacts.size,
            aiChatsCount = aiChats.size,
            expectedAIPersonas = AIPersonaFactory.getAllPersonas().size,
            isSetupComplete = currentUser != null && 
                            aiUsers.size == AIPersonaFactory.getAllPersonas().size &&
                            aiContacts.isNotEmpty() &&
                            aiChats.isNotEmpty()
        )
    }
    
    /**
     * Reset and regenerate mock data with AI personas.
     */
    suspend fun resetMockDataWithAIPersonas() {
        mockDataStorage.clearAllData()
        mockDataStorage.initializeIfNeeded()
    }
    
    /**
     * Print debug information about AI personas setup.
     */
    fun printAIPersonasDebugInfo() {
        val status = verifyAIPersonasSetup()
        val aiPersonas = getAllAIPersonasInfo()
        
        println("=== AI Personas Debug Info ===")
        println("Setup Status: ${if (status.isSetupComplete) "✅ Complete" else "❌ Incomplete"}")
        println("Current User: ${if (status.hasCurrentUser) "✅ Found" else "❌ Missing"}")
        println("Total Users: ${status.totalUsers}")
        println("AI Users: ${status.aiUsersCount}/${status.expectedAIPersonas}")
        println("AI Contacts: ${status.aiContactsCount}")
        println("AI Chats: ${status.aiChatsCount}")
        println()
        
        println("Available AI Personas:")
        aiPersonas.forEach { persona ->
            println("  • ${persona.name} (@${persona.userHandle})")
            println("    Description: ${persona.description}")
            println("    User ID: ${persona.userId}")
            println()
        }
        
        if (!status.isSetupComplete) {
            println("⚠️  Setup Issues:")
            if (!status.hasCurrentUser) println("  - No current user found")
            if (status.aiUsersCount != status.expectedAIPersonas) {
                println("  - Missing AI users (${status.aiUsersCount}/${status.expectedAIPersonas})")
            }
            if (status.aiContactsCount == 0) println("  - No AI contacts found")
            if (status.aiChatsCount == 0) println("  - No AI chats found")
        }
        println("===============================")
    }
}

/**
 * Information about an AI persona for testing.
 */
data class AIPersonaInfo(
    val personaId: String,
    val userId: String,
    val name: String,
    val description: String,
    val userHandle: String,
    val displayName: String,
    val isOnline: Boolean
)

/**
 * Status of AI persona setup in mock data.
 */
data class AIPersonaSetupStatus(
    val hasCurrentUser: Boolean,
    val totalUsers: Int,
    val aiUsersCount: Int,
    val aiContactsCount: Int,
    val aiChatsCount: Int,
    val expectedAIPersonas: Int,
    val isSetupComplete: Boolean
)
