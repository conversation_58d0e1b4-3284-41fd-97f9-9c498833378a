# AI Persona Testing Guide

This guide explains how to test the complete chat and contact functionality of the Meena app using AI personas in the mock backend environment.

## Overview

The mock backend now includes 6 AI personas that are automatically added as contacts and available for chat testing. Each AI persona has a unique personality and responds intelligently to your messages.

## Available AI Personas

### 1. **Friendly Assistant** (@friendlyassistant)
- **Personality**: Helpful and warm, always ready to assist
- **Traits**: High friendliness (0.9), high helpfulness (0.95), moderate chattiness (0.7)
- **Best for testing**: General conversation, help requests, friendly interactions

### 2. **Tech Enthusiast** (@techenthusiast)
- **Personality**: Passionate about technology and innovation
- **Traits**: High enthusiasm (0.9), high chattiness (0.8), moderate formality (0.4)
- **Best for testing**: Tech discussions, project conversations, innovation topics

### 3. **Casual Friend** (@casualfriend)
- **Personality**: Laid-back and easy-going
- **Traits**: Low formality (0.1), high humor (0.8), moderate friendliness (0.8)
- **Best for testing**: Casual conversations, informal chats, relaxed interactions

### 4. **Professional Colleague** (@professionalcolleague)
- **Personality**: Business-focused and professional
- **Traits**: High formality (0.9), high helpfulness (0.9), low humor (0.3)
- **Best for testing**: Professional conversations, formal communications, work-related topics

### 5. **Humorous Buddy** (@humorousbuddy)
- **Personality**: Always ready with a joke or funny comment
- **Traits**: Very high humor (0.95), high chattiness (0.9), low formality (0.1)
- **Best for testing**: Fun conversations, joke exchanges, light-hearted interactions

### 6. **Supportive Mentor** (@supportivementor)
- **Personality**: Encouraging and wise, provides guidance
- **Traits**: High helpfulness (0.95), moderate formality (0.6), high enthusiasm (0.7)
- **Best for testing**: Advice seeking, mentorship conversations, supportive interactions

## Testing Scenarios

### 1. Contact Management Testing

#### View AI Personas in Contacts List
1. Open the Contacts screen
2. Verify all 6 AI personas appear in your contacts list
3. Check that they are marked as "AI" in their display names
4. Verify they show as "online" status (AI personas are always online)
5. Confirm they are marked as favorites (⭐) for easy access

#### Contact Operations
1. **View Contact Details**: Tap on any AI persona to view their profile
2. **Edit Contact**: Try editing the display name or notes for an AI persona
3. **Search Contacts**: Use the search function to find specific AI personas
4. **Contact Groups**: Add AI personas to contact groups

### 2. Chat Functionality Testing

#### Starting Conversations
1. From Contacts: Tap on an AI persona and select "Start Chat"
2. From Chat List: Look for existing chats with AI personas
3. Verify that each AI persona has a welcome message already in the chat

#### Message Testing
1. **Text Messages**: Send various types of messages:
   - Greetings: "Hello", "Hi there", "Good morning"
   - Questions: "How are you?", "What do you think about..."
   - Statements: Share your thoughts or experiences
   - Goodbyes: "See you later", "Goodbye", "Take care"

2. **AI Response Verification**:
   - Verify AI responds within 1-5 seconds
   - Check that responses match the persona's personality
   - Confirm responses are contextually appropriate
   - Test that different personas respond differently to the same message

#### Advanced Chat Features
1. **Message Status**: Verify messages show as sent/delivered/read
2. **Message Reactions**: Add reactions (👍, ❤️, etc.) to AI messages
3. **Message Replies**: Reply to specific AI messages
4. **Message Editing**: Edit your own messages in AI chats
5. **Message Deletion**: Delete messages for self or everyone

### 3. Personality Testing

#### Test Different Personalities
Send the same message to different AI personas and observe different responses:

**Test Message**: "I'm working on a new project"

Expected responses:
- **Friendly Assistant**: "That sounds exciting! How can I help you with your project?"
- **Tech Enthusiast**: "Awesome! What kind of tech are you using? I'd love to hear about it!"
- **Casual Friend**: "Cool! What's it about?"
- **Professional Colleague**: "Excellent. Please let me know if you need any assistance with the project."
- **Humorous Buddy**: "A new project? Is it world domination? Because I'm in! 😄"
- **Supportive Mentor**: "That's wonderful! New projects are great opportunities for growth."

### 4. Conversation Flow Testing

#### Test Natural Conversations
1. Start with a greeting
2. Have a back-and-forth conversation (5-10 messages)
3. Change topics mid-conversation
4. End with a goodbye
5. Verify AI maintains context and personality throughout

#### Test Edge Cases
1. Send very short messages ("ok", "yes", "no")
2. Send very long messages (multiple sentences)
3. Send messages with emojis
4. Send messages with questions
5. Send messages with technical terms

### 5. Multi-Chat Testing

#### Simultaneous Conversations
1. Open chats with multiple AI personas
2. Send messages to different personas simultaneously
3. Verify each responds according to their personality
4. Check that conversations don't interfere with each other

### 6. App Integration Testing

#### Navigation Testing
1. Switch between Contacts and Chat screens
2. Navigate from contact details to chat
3. Use back navigation and verify state is maintained
4. Test app backgrounding/foregrounding during AI conversations

#### Performance Testing
1. Send rapid messages to AI personas
2. Verify app remains responsive
3. Check memory usage during extended conversations
4. Test with multiple AI chats open

## Debugging and Verification

### Using MockTestingUtils

You can use the built-in testing utilities to verify the setup:

```kotlin
// In your debug code or test
val mockTestingUtils = // inject or get instance
mockTestingUtils.printAIPersonasDebugInfo()
```

This will print detailed information about:
- Setup completion status
- Number of AI users, contacts, and chats
- List of all available AI personas
- Any setup issues

### Manual Verification Checklist

- [ ] 6 AI personas appear in contacts list
- [ ] All AI personas are marked as favorites
- [ ] AI personas show as "online"
- [ ] Each AI persona has a chat with welcome message
- [ ] AI personas respond to messages within 5 seconds
- [ ] Responses match expected personality traits
- [ ] Different personas give different responses to same input
- [ ] Conversation context is maintained
- [ ] All chat features work with AI personas
- [ ] App performance remains good during AI interactions

## Troubleshooting

### AI Personas Not Appearing
1. Check that `AppConfig.useMockBackend` is `true`
2. Verify mock data has been initialized
3. Try resetting mock data: `mockTestingUtils.resetMockDataWithAIPersonas()`

### AI Not Responding
1. Check that `AppConfig.FeatureFlags.ENABLE_AI_CHAT_RESPONSES` is `true`
2. Verify you're in a one-to-one chat with an AI persona
3. Check network delay settings in `AppConfig.MockConfig`

### Responses Don't Match Personality
1. Verify the correct persona ID is being used
2. Check AI persona definitions in `AIPersonaFactory`
3. Review response patterns and personality traits

## Expected Benefits

After completing this testing, you should have:

1. **Verified Contact Management**: All contact operations work correctly with AI personas
2. **Validated Chat Functionality**: Complete chat workflow functions properly
3. **Confirmed AI Integration**: AI responses work as expected with appropriate personalities
4. **Tested User Experience**: App provides smooth interaction with AI personas
5. **Identified Issues**: Any bugs or improvements needed for the final implementation

This comprehensive testing approach ensures that both the contacts module and chat module work correctly with the mock backend, providing a solid foundation for further development and testing.
